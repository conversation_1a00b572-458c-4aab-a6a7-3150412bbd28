-- =====================================================================================
-- COMPLETEVIEW DATABASE ANONYMIZATION - STANDALONE MODULE
-- File: 05_Network_Device_Anonymization.sql

-- =====================================================================================
-- This is a standalone, pre-configured SQL module that can be executed directly
-- in SQL Server Management Studio without requiring PowerShell configuration.
-- 
-- IMPORTANT: 
-- 1. BACKUP YOUR DATABASE BEFORE RUNNING THIS SCRIPT
-- 2. Review the configuration values below before execution
-- 3. Execute modules in numerical order (01, 02, 03, etc.)
-- =====================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON;

-- =====================================================================================
-- CENTRALIZED CONFIGURATION LOADER
-- =====================================================================================

-- Verify configuration prerequisites
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig')
BEGIN
    RAISERROR('AnonymizationConfig table not found. Please run Module 01 (Setup and Validation) first.', 16, 1);
END

IF OBJECT_ID('dbo.fn_GetConfigValue') IS NULL
BEGIN
    RAISERROR('Configuration helper function fn_GetConfigValue not found. Please run Module 01 (Setup and Validation) first.', 16, 1);
END

-- Load configuration values from centralized table
DECLARE @ExecutionID VARCHAR(50) = 'MODULE05_' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '');
DECLARE @ModuleName VARCHAR(100) = 'Network Device Anonymization';

PRINT 'Starting ' + @ModuleName + '...';
PRINT 'Execution ID: ' + @ExecutionID;

DECLARE @AnonymizationSeed INT = CAST(dbo.fn_GetConfigValue('AnonymizationSeed') AS INT);
DECLARE @BatchSize INT = CAST(dbo.fn_GetConfigValue('BatchSize') AS INT);
DECLARE @DryRun BIT = CAST(dbo.fn_GetConfigValue('DryRun') AS BIT);
DECLARE @CommitChanges BIT = CAST(dbo.fn_GetConfigValue('CommitChanges') AS BIT);

-- Network device specific configuration
DECLARE @CameraIPRangeStart NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Cameras');
DECLARE @AlarmIPRangeStart NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Alarms');
DECLARE @AudioIPRangeStart NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Audio');
DECLARE @ServerIPRange NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Servers');
DECLARE @VideoDeviceIPRange NVARCHAR(20) = @CameraIPRangeStart;

-- Initialize module variables
DECLARE @TotalRowsProcessed INT = 0;
DECLARE @ModuleStartTime DATETIME = GETDATE();
DECLARE @OperationStart DATETIME;
DECLARE @RowsAffected INT;

PRINT 'Configuration loaded from centralized table for Network Device Anonymization';

-- =====================================================================================
-- CREATE HELPER FUNCTIONS
-- =====================================================================================

-- Create anonymization hash function if it doesn't exist
IF OBJECT_ID('dbo.fn_AnonymizationHash') IS NULL
BEGIN
    EXEC('
    CREATE FUNCTION dbo.fn_AnonymizationHash(@InputValue INT, @Seed INT, @Salt NVARCHAR(50))
    RETURNS INT
    AS
    BEGIN
        DECLARE @Result INT;
        SET @Result = ABS(CHECKSUM(CAST(@InputValue AS NVARCHAR) + CAST(@Seed AS NVARCHAR) + @Salt));
        RETURN @Result;
    END
    ');
END

-- Create sequence function if it doesn't exist
IF OBJECT_ID('dbo.fn_GetAnonymizedSequence') IS NULL
BEGIN
    EXEC('
    CREATE FUNCTION dbo.fn_GetAnonymizedSequence(@InputValue INT, @Seed INT, @Type NVARCHAR(50))
    RETURNS INT
    AS
    BEGIN
        DECLARE @Result INT;
        SET @Result = ABS(CHECKSUM(CAST(@InputValue AS NVARCHAR) + CAST(@Seed AS NVARCHAR) + @Type)) % 999999 + 1;
        RETURN @Result;
    END
    ');
END

-- =====================================================================================
-- MODULE 05: NETWORK DEVICE ANONYMIZATION
-- =====================================================================================
-- Description: Anonymizes network device data including IP addresses, MAC addresses,
--              device names, and configuration details for cameras, alarms, and audio devices
-- =====================================================================================

PRINT '=======================================================================';
PRINT 'MODULE 05: NETWORK DEVICE ANONYMIZATION';
PRINT '=======================================================================';
PRINT 'Execution Mode: ' + CASE WHEN @DryRun = 1 THEN 'DRY RUN (Preview Only)' ELSE 'LIVE EXECUTION' END;
PRINT '';

BEGIN TRY
    BEGIN TRAN;

    -- Create pre-anonymization snapshot
    PRINT 'Creating pre-anonymization snapshot...';
    IF OBJECT_ID('dbo.sp_CreatePreAnonymizationSnapshot') IS NOT NULL
    BEGIN
        EXEC dbo.sp_CreatePreAnonymizationSnapshot 
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END

    -- =====================================================================================
    -- VIDEO DEVICES ANONYMIZATION
    -- =====================================================================================
    PRINT 'Processing VideoDevices table...';

    -- Check if VideoDevices table exists
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'VideoDevices')
    BEGIN
        DECLARE @VideoDeviceRowCount INT = (SELECT COUNT(*) FROM dbo.VideoDevices);
        SET @OperationStart = GETDATE();
        
        IF @VideoDeviceRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@VideoDeviceRowCount AS VARCHAR) + ' video device records to process.';
            
            -- Preview changes
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Video device anonymization preview:';
                SELECT TOP 10
                    VideoDeviceId,
                    CONCAT('CAM-', RIGHT('0000' + CAST(ROW_NUMBER() OVER (ORDER BY VideoDeviceId) AS VARCHAR), 4)) AS New_Manufacturer,
                    CONCAT(@CameraIPRangeStart, '.', 
                        CAST((ROW_NUMBER() OVER (ORDER BY VideoDeviceId) - 1) / 254 + 1 AS VARCHAR), '.', 
                        CAST((ROW_NUMBER() OVER (ORDER BY VideoDeviceId) - 1) % 254 + 1 AS VARCHAR)) AS New_IpAddressV4,
                    CONCAT('02:00:00:', 
                        RIGHT('00' + CAST((VideoDeviceId % 255) AS VARCHAR), 2), ':', 
                        RIGHT('00' + CAST(((VideoDeviceId * 7) % 255) AS VARCHAR), 2), ':', 
                        RIGHT('00' + CAST(((VideoDeviceId * 13) % 255) AS VARCHAR), 2)) AS New_MACAddress,
                    '[ANONYMIZED]' AS New_Model,
                    '[ANONYMIZED]' AS New_SerialNumber
                FROM dbo.VideoDevices
                WHERE IpAddressV4 IS NOT NULL OR MacAddress IS NOT NULL;
                
                SET @RowsAffected = @VideoDeviceRowCount;
            END
            ELSE
            BEGIN
                -- Anonymize manufacturer names using CTE
                WITH ManufacturerCTE AS (
                    SELECT VideoDeviceId,
                           CONCAT('CAM-', RIGHT('0000' + CAST(ROW_NUMBER() OVER (ORDER BY VideoDeviceId) AS VARCHAR), 4)) AS NewManufacturer
                    FROM dbo.VideoDevices 
                    WHERE Manufacturer IS NOT NULL
                )
                UPDATE vd 
                SET Manufacturer = cte.NewManufacturer
                FROM dbo.VideoDevices vd
                INNER JOIN ManufacturerCTE cte ON vd.VideoDeviceId = cte.VideoDeviceId;
                
                DECLARE @VideoNameUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize IP addresses using sequential assignment to prevent collisions
                WITH VideoDeviceIPCTE AS (
                    SELECT VideoDeviceId,
                           ROW_NUMBER() OVER (ORDER BY VideoDeviceId) AS RowNum
                    FROM dbo.VideoDevices 
                    WHERE IpAddressV4 IS NOT NULL
                )
                UPDATE vd 
                SET IpAddressV4 = CONCAT(@CameraIPRangeStart, '.', 
                    CAST((cte.RowNum - 1) / 254 + 1 AS VARCHAR), '.', 
                    CAST((cte.RowNum - 1) % 254 + 1 AS VARCHAR))
                FROM dbo.VideoDevices vd
                INNER JOIN VideoDeviceIPCTE cte ON vd.VideoDeviceId = cte.VideoDeviceId;
                
                DECLARE @VideoIPUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize MAC addresses using deterministic hash
                WITH MACAddressCTE AS (
                    SELECT VideoDeviceId,
                           CONCAT('02:00:00:', 
                                  RIGHT('00' + CAST((dbo.fn_AnonymizationHash(VideoDeviceId, @AnonymizationSeed, 'MAC1') % 255) AS VARCHAR), 2), ':', 
                                  RIGHT('00' + CAST((dbo.fn_AnonymizationHash(VideoDeviceId, @AnonymizationSeed, 'MAC2') % 255) AS VARCHAR), 2), ':', 
                                  RIGHT('00' + CAST((dbo.fn_AnonymizationHash(VideoDeviceId, @AnonymizationSeed, 'MAC3') % 255) AS VARCHAR), 2)) AS NewMACAddress
                    FROM dbo.VideoDevices 
                    WHERE MacAddress IS NOT NULL
                )
                UPDATE vd 
                SET MacAddress = cte.NewMACAddress
                FROM dbo.VideoDevices vd
                INNER JOIN MACAddressCTE cte ON vd.VideoDeviceId = cte.VideoDeviceId;
                
                DECLARE @VideoMACUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize model information
                UPDATE dbo.VideoDevices 
                SET Model = '[ANONYMIZED]'
                WHERE Model IS NOT NULL;
                
                DECLARE @VideoModelUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize serial numbers
                UPDATE dbo.VideoDevices 
                SET SerialNumber = '[ANONYMIZED]'
                WHERE SerialNumber IS NOT NULL;
                
                DECLARE @VideoSerialUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize firmware information
                UPDATE dbo.VideoDevices 
                SET Firmware = '[ANONYMIZED]'
                WHERE Firmware IS NOT NULL;
                
                DECLARE @VideoFirmwareUpdateCount INT = @@ROWCOUNT;
                
                SET @RowsAffected = @VideoNameUpdateCount + @VideoIPUpdateCount + @VideoMACUpdateCount + @VideoModelUpdateCount + @VideoSerialUpdateCount + @VideoFirmwareUpdateCount;
                
                -- Buffer operations for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES 
                    ('Network Data', 'VideoDevices', 'Manufacturer', @VideoNameUpdateCount, 'Sequential Naming', @OperationStart, GETDATE(),
                     'Video device manufacturers anonymized with CAM-#### pattern'),
                    ('Network Data', 'VideoDevices', 'IpAddressV4', @VideoIPUpdateCount, 'IP Range Assignment', @OperationStart, GETDATE(),
                     CONCAT('Video device IP addresses anonymized to ', @CameraIPRangeStart, '.x.x range')),
                    ('Network Data', 'VideoDevices', 'MacAddress', @VideoMACUpdateCount, 'Pattern Generation', @OperationStart, GETDATE(),
                     'Video device MAC addresses anonymized'),
                    ('Network Data', 'VideoDevices', 'Model', @VideoModelUpdateCount, 'Generic Replacement', @OperationStart, GETDATE(),
                     'Video device models anonymized'),
                    ('Network Data', 'VideoDevices', 'SerialNumber', @VideoSerialUpdateCount, 'Generic Replacement', @OperationStart, GETDATE(),
                     'Video device serial numbers anonymized'),
                    ('Network Data', 'VideoDevices', 'Firmware', @VideoFirmwareUpdateCount, 'Generic Replacement', @OperationStart, GETDATE(),
                     'Video device firmware information anonymized');
                
                PRINT 'Updated ' + CAST(@VideoNameUpdateCount AS VARCHAR) + ' video device manufacturers';
                PRINT 'Updated ' + CAST(@VideoIPUpdateCount AS VARCHAR) + ' video device IP addresses';
                PRINT 'Updated ' + CAST(@VideoMACUpdateCount AS VARCHAR) + ' video device MAC addresses';
                PRINT 'Updated ' + CAST(@VideoModelUpdateCount AS VARCHAR) + ' video device models';
                PRINT 'Updated ' + CAST(@VideoSerialUpdateCount AS VARCHAR) + ' video device serial numbers';
                PRINT 'Updated ' + CAST(@VideoFirmwareUpdateCount AS VARCHAR) + ' video device firmware information';
            END
            
            SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;
        END
        ELSE
        BEGIN
            PRINT 'No video device records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'VideoDevices table not found in database.';
    END

    -- =====================================================================================
    -- IP SETTINGS ANONYMIZATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing IP Settings...';

    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'IpSettings')
    BEGIN
        DECLARE @IpSettingsCount INT = (SELECT COUNT(*) FROM dbo.IpSettings WHERE IpAddress IS NOT NULL);
        SET @OperationStart = GETDATE();
        
        IF @IpSettingsCount > 0
        BEGIN
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: IP Settings anonymization preview:';
                SELECT TOP 10
                    IpSettingsId,
                    IpAddress AS Current_IpAddress,
                    CONCAT(@CameraIPRangeStart, '.', ((IpSettingsId % 253) + 150)) AS New_IpAddress
                FROM dbo.IpSettings
                WHERE IpAddress IS NOT NULL;
            END
            ELSE
            BEGIN
                -- Anonymize IP addresses for settings
                UPDATE dbo.IpSettings
                SET IpAddress = CONCAT(@CameraIPRangeStart, '.', ((IpSettingsId % 253) + 150))
                WHERE IpAddress IS NOT NULL;
                
                DECLARE @IpSettingsUpdateCount INT = @@ROWCOUNT;
                
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Network Data', 'IpSettings', 'IpAddress', @IpSettingsUpdateCount, 'IP Range Assignment', @OperationStart, GETDATE(),
                        CONCAT('IP settings anonymized to ', @CameraIPRangeStart, '.x range'));
                
                PRINT 'Updated ' + CAST(@IpSettingsUpdateCount AS VARCHAR) + ' IP settings records';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @IpSettingsUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No IP settings records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'IpSettings table not found in database.';
    END

    -- =====================================================================================
    -- IPV6 ADDRESS ANONYMIZATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing IPv6 addresses...';

    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'VideoDevices')
    BEGIN
        IF EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.VideoDevices') AND name = 'IpAddressV6')
        BEGIN
            DECLARE @VideoV6Count INT = (SELECT COUNT(*) FROM dbo.VideoDevices WHERE IpAddressV6 IS NOT NULL);
            
            IF @VideoV6Count > 0
            BEGIN
                IF @DryRun = 1
                BEGIN
                    PRINT 'DRY RUN: Video Device IPv6 anonymization preview:';
                    SELECT TOP 10
                        VideoDeviceId,
                        IpAddressV6 AS Current_IpAddressV6,
                        CONCAT('2001:db8::', CAST((VideoDeviceId % 65535) AS VARCHAR)) AS New_IpAddressV6
                    FROM dbo.VideoDevices
                    WHERE IpAddressV6 IS NOT NULL;
                END
                ELSE
                BEGIN
                    -- Anonymize IPv6 addresses using RFC 3849 documentation prefix
                    UPDATE dbo.VideoDevices
                    SET IpAddressV6 = CONCAT('2001:db8::', CAST((VideoDeviceId % 65535) AS VARCHAR))
                    WHERE IpAddressV6 IS NOT NULL;
                    
                    DECLARE @VideoV6UpdateCount INT = @@ROWCOUNT;
                    
                    INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                    VALUES ('Network Data', 'VideoDevices', 'IpAddressV6', @VideoV6UpdateCount, 'IPv6 Anonymization', @OperationStart, GETDATE(),
                            'Video device IPv6 addresses anonymized using RFC 3849 documentation prefix');
                    
                    PRINT 'Updated ' + CAST(@VideoV6UpdateCount AS VARCHAR) + ' video device IPv6 addresses';
                    SET @TotalRowsProcessed = @TotalRowsProcessed + @VideoV6UpdateCount;
                END
            END
            ELSE
            BEGIN
                PRINT 'No VideoDevices IPv6 records found to anonymize.';
            END
        END
        ELSE
        BEGIN
            PRINT 'IpAddressV6 column not found in VideoDevices table';
        END
    END

    -- =====================================================================================
    -- WALL VIEW AGENT HOST IPs
    -- =====================================================================================
    PRINT '';
    PRINT 'Anonymizing Wall View Agent host IPs...';

    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'WallViewAgent')
    BEGIN
        DECLARE @WallViewCount INT = (SELECT COUNT(*) FROM dbo.WallViewAgent WHERE HostIp IS NOT NULL);
        SET @OperationStart = GETDATE();
        
        IF @WallViewCount > 0
        BEGIN
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Wall View Agent host IP anonymization preview:';
                SELECT TOP 10
                    WallViewAgentId,
                    HostIp AS Current_HostIp,
                    CONCAT(@ServerIPRange, '.', ((WallViewAgentId % 253) + 200)) AS New_HostIp
                FROM dbo.WallViewAgent
                WHERE HostIp IS NOT NULL;
            END
            ELSE
            BEGIN
                -- Anonymize Wall View Agent host IPs
                UPDATE dbo.WallViewAgent
                SET HostIp = CONCAT(@ServerIPRange, '.', ((WallViewAgentId % 253) + 200))
                WHERE HostIp IS NOT NULL;
                
                DECLARE @WallViewUpdateCount INT = @@ROWCOUNT;
                
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Network Data', 'WallViewAgent', 'HostIp', @WallViewUpdateCount, 'IP Range Assignment', @OperationStart, GETDATE(),
                        CONCAT('Wall View Agent host IPs anonymized to ', @ServerIPRange, '.x range'));
                
                PRINT 'Updated ' + CAST(@WallViewUpdateCount AS VARCHAR) + ' Wall View Agent host IPs';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @WallViewUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No Wall View Agent host IP records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'WallViewAgent table not found in database.';
    END

    -- =====================================================================================
    -- DEVICE TABLE ANONYMIZATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing Device table...';

    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'Device')
    BEGIN
        DECLARE @DeviceRowCount INT = (SELECT COUNT(*) FROM dbo.Device WHERE DeviceName IS NOT NULL);
        SET @OperationStart = GETDATE();
        
        IF @DeviceRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@DeviceRowCount AS VARCHAR) + ' device records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Device anonymization preview:';
                SELECT TOP 10
                    DeviceRelationId,
                    CONCAT('DEV-', RIGHT('0000' + CAST(ROW_NUMBER() OVER (ORDER BY DeviceRelationId) AS VARCHAR), 4)) AS New_DeviceName
                FROM dbo.Device
                WHERE DeviceName IS NOT NULL;
            END
            ELSE
            BEGIN
                -- Anonymize device names using CTE
                WITH DeviceCTE AS (
                    SELECT DeviceRelationId,
                           CONCAT('DEV-', RIGHT('0000' + CAST(ROW_NUMBER() OVER (ORDER BY DeviceRelationId) AS VARCHAR), 4)) AS NewDeviceName
                    FROM dbo.Device
                    WHERE DeviceName IS NOT NULL
                )
                UPDATE d 
                SET DeviceName = cte.NewDeviceName
                FROM dbo.Device d
                INNER JOIN DeviceCTE cte ON d.DeviceRelationId = cte.DeviceRelationId;
                
                DECLARE @DeviceUpdateCount INT = @@ROWCOUNT;
                
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Network Data', 'Device', 'DeviceName', @DeviceUpdateCount, 'Sequential Naming', @OperationStart, GETDATE(),
                        'Device names anonymized with DEV-#### pattern');
                
                PRINT 'Updated ' + CAST(@DeviceUpdateCount AS VARCHAR) + ' device names';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @DeviceUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No device records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'Device table not found in database.';
    END

    -- =====================================================================================
    -- VIDEO DEVICE URLS ANONYMIZATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing VideoDevice URLs and file paths...';

    -- Check if VideoDevices table exists
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'VideoDevices')
    BEGIN
        DECLARE @VideoUrlRowCount INT = (SELECT COUNT(*) FROM dbo.VideoDevices WHERE 
            OnvifDeviceUrl IS NOT NULL OR OnvifEventsUrl IS NOT NULL OR 
            OnvifPtzUrl IS NOT NULL OR OnvifImageUrl IS NOT NULL OR 
            OnvifIoUrl IS NOT NULL OR OnvifMediaUrl IS NOT NULL OR
            SourceFilePath IS NOT NULL);
        
        IF @VideoUrlRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@VideoUrlRowCount AS VARCHAR) + ' video device records with URLs/paths to anonymize.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: VideoDevices URL anonymization preview:';
                SELECT TOP 10
                    VideoDeviceId,
                    OnvifDeviceUrl,
                    CONCAT('http://', @VideoDeviceIPRange, '.', ((VideoDeviceId % 253) + 1), '/onvif/device_service') AS New_OnvifDeviceUrl,
                    OnvifEventsUrl,
                    CONCAT('http://', @VideoDeviceIPRange, '.', ((VideoDeviceId % 253) + 1), '/onvif/event_service') AS New_OnvifEventsUrl,
                    SourceFilePath,
                    CASE WHEN SourceFilePath IS NOT NULL THEN CONCAT('AnonymizedCams/cam', VideoDeviceId, '.avi') ELSE NULL END AS New_SourceFilePath
                FROM dbo.VideoDevices
                WHERE OnvifDeviceUrl IS NOT NULL OR OnvifEventsUrl IS NOT NULL OR SourceFilePath IS NOT NULL;
            END
            ELSE
            BEGIN
                -- Anonymize OnvifDeviceUrl
                UPDATE dbo.VideoDevices 
                SET OnvifDeviceUrl = CONCAT('http://', @VideoDeviceIPRange, '.', ((VideoDeviceId % 253) + 1), '/onvif/device_service')
                WHERE OnvifDeviceUrl IS NOT NULL AND OnvifDeviceUrl != '';
                
                -- Anonymize OnvifEventsUrl
                UPDATE dbo.VideoDevices 
                SET OnvifEventsUrl = CONCAT('http://', @VideoDeviceIPRange, '.', ((VideoDeviceId % 253) + 1), '/onvif/event_service')
                WHERE OnvifEventsUrl IS NOT NULL AND OnvifEventsUrl != '';
                
                -- Anonymize OnvifPtzUrl
                UPDATE dbo.VideoDevices 
                SET OnvifPtzUrl = CONCAT('http://', @VideoDeviceIPRange, '.', ((VideoDeviceId % 253) + 1), '/onvif/ptz_service')
                WHERE OnvifPtzUrl IS NOT NULL AND OnvifPtzUrl != '';
                
                -- Anonymize OnvifImageUrl
                UPDATE dbo.VideoDevices 
                SET OnvifImageUrl = CONCAT('http://', @VideoDeviceIPRange, '.', ((VideoDeviceId % 253) + 1), '/onvif/image_service')
                WHERE OnvifImageUrl IS NOT NULL AND OnvifImageUrl != '';
                
                -- Anonymize OnvifIoUrl
                UPDATE dbo.VideoDevices 
                SET OnvifIoUrl = CONCAT('http://', @VideoDeviceIPRange, '.', ((VideoDeviceId % 253) + 1), '/onvif/io_service')
                WHERE OnvifIoUrl IS NOT NULL AND OnvifIoUrl != '';
                
                -- Anonymize OnvifMediaUrl
                UPDATE dbo.VideoDevices 
                SET OnvifMediaUrl = CONCAT('http://', @VideoDeviceIPRange, '.', ((VideoDeviceId % 253) + 1), '/onvif/media_service')
                WHERE OnvifMediaUrl IS NOT NULL AND OnvifMediaUrl != '';
                
                -- Anonymize SourceFilePath
                UPDATE dbo.VideoDevices 
                SET SourceFilePath = CONCAT('AnonymizedCams/cam', VideoDeviceId, '.avi')
                WHERE SourceFilePath IS NOT NULL AND SourceFilePath != '';
                
                DECLARE @VideoUrlUpdateCount INT = @@ROWCOUNT;
                
                -- Log changes
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Network Data', 'VideoDevices', 'ONVIF_URLs', @VideoUrlUpdateCount, 'URL_Anonymization', @OperationStart, GETDATE(),
                        'Anonymized ONVIF URLs and source file paths');
                
                PRINT 'VideoDevices ONVIF URLs and file paths anonymized.';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @VideoUrlUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No VideoDevices URL data found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'VideoDevices table not found in database.';
    END

    -- =====================================================================================
    -- MEDIA PROFILE URLS ANONYMIZATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing MediaProfile URLs...';

    -- Check if MediaProfile table exists
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'MediaProfile')
    BEGIN
        DECLARE @MediaUrlRowCount INT = (SELECT COUNT(*) FROM dbo.MediaProfile WHERE 
            StreamUrl IS NOT NULL OR SnapshotUrl IS NOT NULL);
        
        IF @MediaUrlRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@MediaUrlRowCount AS VARCHAR) + ' media profile records with URLs to anonymize.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: MediaProfile URL anonymization preview:';
                SELECT TOP 10
                    MediaProfileId,
                    StreamUrl,
                    CONCAT('/stream/profile_', MediaProfileId, '_anonymized') AS New_StreamUrl,
                    SnapshotUrl,
                    CONCAT('/snapshot/profile_', MediaProfileId, '_anonymized.jpg') AS New_SnapshotUrl
                FROM dbo.MediaProfile
                WHERE StreamUrl IS NOT NULL OR SnapshotUrl IS NOT NULL;
            END
            ELSE
            BEGIN
                -- Anonymize StreamUrl
                UPDATE dbo.MediaProfile 
                SET StreamUrl = CONCAT('/stream/profile_', MediaProfileId, '_anonymized')
                WHERE StreamUrl IS NOT NULL AND StreamUrl != '';
                
                -- Anonymize SnapshotUrl
                UPDATE dbo.MediaProfile 
                SET SnapshotUrl = CONCAT('/snapshot/profile_', MediaProfileId, '_anonymized.jpg')
                WHERE SnapshotUrl IS NOT NULL AND SnapshotUrl != '';
                
                DECLARE @MediaUrlUpdateCount INT = @@ROWCOUNT;
                
                -- Log changes
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Network Data', 'MediaProfile', 'Stream_URLs', @MediaUrlUpdateCount, 'URL_Anonymization', @OperationStart, GETDATE(),
                        'Anonymized streaming and snapshot URLs');
                
                PRINT 'MediaProfile URLs anonymized.';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @MediaUrlUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No MediaProfile URL data found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'MediaProfile table not found in database.';
    END

    -- =====================================================================================
    -- MODULE COMPLETION
    -- =====================================================================================
    DECLARE @ModuleEndTime DATETIME = GETDATE();
    DECLARE @ModuleExecutionTime INT = DATEDIFF(MILLISECOND, @ModuleStartTime, @ModuleEndTime);

    -- Flush aggregated logging buffer to reduce transaction log bloat
    IF OBJECT_ID('dbo.sp_FlushAnonymizationBuffer') IS NOT NULL
    BEGIN
        EXEC dbo.sp_FlushAnonymizationBuffer @DryRun = @DryRun;
    END

    -- Log module completion
    INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, ExecutionTimeMs, BatchProcessed, Notes)
    VALUES ('Network Device', 'MODULE_COMPLETE', 'ALL', @TotalRowsProcessed, 'Module Execution', 
            @ModuleExecutionTime, 0, 
            'Network device anonymization completed in ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds');

    PRINT '';
    PRINT '=======================================================================';
    PRINT 'NETWORK DEVICE ANONYMIZATION MODULE COMPLETED';
    PRINT '=======================================================================';
    PRINT 'Total execution time: ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds';
    PRINT 'Total rows processed: ' + CAST(@TotalRowsProcessed AS VARCHAR);
    PRINT 'Operations completed: Video devices, alarm devices, audio devices, IP settings, NVR settings, URLs';
    PRINT 'Dry run mode: ' + CASE WHEN @DryRun = 1 THEN 'YES (No data modified)' ELSE 'NO (Data modified)' END;
    PRINT '=======================================================================';

    -- Create post-anonymization snapshot
    IF @DryRun = 0 AND OBJECT_ID('dbo.sp_CreatePostAnonymizationSnapshot') IS NOT NULL
    BEGIN
        PRINT 'Creating post-anonymization snapshot...';
        EXEC dbo.sp_CreatePostAnonymizationSnapshot 
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END

    IF @DryRun = 1 OR @CommitChanges = 0 
        ROLLBACK TRAN 
    ELSE 
        COMMIT TRAN;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK TRAN;
    
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    
    PRINT 'ERROR in Network Device Anonymization: ' + @ErrorMessage;
    
    -- Log the error if possible
    IF OBJECT_ID('dbo.AnonymizationLog') IS NOT NULL
    BEGIN
        INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, Notes)
        VALUES ('Network Device', 'ERROR', 'MODULE_FAILURE', 0, 'Error', @ErrorMessage);
    END
    
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH