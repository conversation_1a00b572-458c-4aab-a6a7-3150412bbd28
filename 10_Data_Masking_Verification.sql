-- =====================================================================================
-- COMPLETEVIEW DATABASE ANONYMIZATION - STANDALONE MODULE
-- File: 10_Data_Masking_Verification.sql

-- =====================================================================================
-- This is a standalone, pre-configured SQL module that can be executed directly
-- in SQL Server Management Studio without requiring PowerShell configuration.
-- 
-- IMPORTANT: 
-- 1. BACKUP YOUR DATABASE BEFORE RUNNING THIS SCRIPT
-- 2. Review the configuration values below before execution
-- 3. Execute modules in numerical order (01, 02, 03, etc.)
-- =====================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON;

-- =====================================================================================
-- CENTRALIZED CONFIGURATION LOADER
-- =====================================================================================

-- Verify configuration prerequisites
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig')
BEGIN
    RAISERROR('AnonymizationConfig table not found. Please run Module 01 (Setup and Validation) first.', 16, 1);
END

IF OBJECT_ID('dbo.fn_GetConfigValue') IS NULL
BEGIN
    RAISERROR('Configuration helper function fn_GetConfigValue not found. Please run Module 01 (Setup and Validation) first.', 16, 1);
END

-- Load configuration values from centralized table
DECLARE @ExecutionID VARCHAR(50) = 'MODULE10_' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '');
DECLARE @ModuleName VARCHAR(100) = 'Data Masking Verification';

PRINT 'Starting ' + @ModuleName + '...';
PRINT 'Execution ID: ' + @ExecutionID;

DECLARE @AnonymizationSeed INT = CAST(dbo.fn_GetConfigValue('AnonymizationSeed') AS INT);
DECLARE @AnonymizedDomain NVARCHAR(100) = dbo.fn_GetConfigValue('AnonymizedDomain');
DECLARE @EmailSuffix NVARCHAR(100) = dbo.fn_GetConfigValue('EmailSuffix');
DECLARE @DefaultPassword NVARCHAR(100) = dbo.fn_GetConfigValue('DefaultPassword');
DECLARE @BatchSize INT = CAST(dbo.fn_GetConfigValue('BatchSize') AS INT);
DECLARE @DryRun BIT = CAST(dbo.fn_GetConfigValue('DryRun') AS BIT);
DECLARE @CommitChanges BIT = CAST(dbo.fn_GetConfigValue('CommitChanges') AS BIT);
DECLARE @UsePerformanceOptimizations BIT = CAST(dbo.fn_GetConfigValue('UsePerformanceOptimizations') AS BIT);
DECLARE @RequireRecentBackup BIT = CAST(dbo.fn_GetConfigValue('RequireRecentBackup') AS BIT);
DECLARE @BackupMaxAgeDays INT = CAST(dbo.fn_GetConfigValue('BackupMaxAgeDays') AS INT);

-- IP and path configuration
DECLARE @CVE_IPRange_Cameras NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Cameras');
DECLARE @CVE_IPRange_Servers NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Servers');
DECLARE @CVE_IPRange_SessionManager NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_SessionManager');
DECLARE @CVE_FilePath_Prefix NVARCHAR(100) = dbo.fn_GetConfigValue('CVE_FilePath_Prefix');
DECLARE @CVE_UNCPath_Prefix NVARCHAR(100) = dbo.fn_GetConfigValue('CVE_UNCPath_Prefix');
DECLARE @CVE_StreamProfile_Prefix NVARCHAR(50) = dbo.fn_GetConfigValue('CVE_StreamProfile_Prefix');

-- Initialize module variables
DECLARE @TotalRowsProcessed INT = 0;
DECLARE @ModuleStartTime DATETIME = GETDATE();

PRINT 'Configuration loaded from centralized table for Data Masking Verification';

-- =====================================================================================
-- MODULE 10: DATA MASKING VERIFICATION
-- =====================================================================================
-- Description: Deep analysis to verify no real data patterns remain
-- Security Level: CRITICAL - Final security validation
-- =====================================================================================

PRINT '=======================================================================';
PRINT 'MODULE 10: DATA MASKING VERIFICATION';
PRINT '=======================================================================';
PRINT 'Execution Mode: ' + CASE WHEN @DryRun = 1 THEN 'DRY RUN (Preview Only)' ELSE 'LIVE EXECUTION' END;
PRINT '';

BEGIN TRY
    BEGIN TRAN;

    -- =====================================================================================
    -- PATTERN DETECTION RESULTS TABLE
    -- =====================================================================================
    PRINT 'Initializing advanced pattern detection framework...';

    -- Create comprehensive pattern detection results table
    CREATE TABLE #PatternDetectionResults (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        Category VARCHAR(50),
        TableName VARCHAR(100),
        ColumnName VARCHAR(100),
        PatternType VARCHAR(100),
        SampleValue NVARCHAR(500),
        RecordCount INT,
        Severity VARCHAR(20),
        Recommendation NVARCHAR(1000),
        DetectionTime DATETIME DEFAULT GETDATE()
    );

    -- Initialize counters
    DECLARE @HighRiskCount INT = 0;
    DECLARE @MediumRiskCount INT = 0;
    DECLARE @LowRiskCount INT = 0;
    DECLARE @TotalIssues INT = 0;

    PRINT 'Advanced pattern detection framework initialized.';
    PRINT '';

    -- =====================================================================================
    -- SNAPSHOT-BASED VERIFICATION (PRIMARY METHOD)
    -- =====================================================================================
    PRINT 'SNAPSHOT-BASED MISSED ANONYMIZATION DETECTION';
    PRINT '=======================================================================';

    -- Check for snapshot comparison results first (most reliable method)
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationPreSnapshot')
       AND EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationPostSnapshot')
    BEGIN
        -- Get the most recent execution for comparison
        DECLARE @LatestExecutionID VARCHAR(50);
        SELECT TOP 1 @LatestExecutionID = ExecutionID 
        FROM dbo.AnonymizationPreSnapshot 
        ORDER BY SnapshotDate DESC;
        
        IF @LatestExecutionID IS NOT NULL
        BEGIN
            PRINT 'Performing snapshot-based verification...';
            
            -- Execute detailed snapshot comparison with miss detection
            IF OBJECT_ID('dbo.sp_CompareSnapshotsAndDetectMisses') IS NOT NULL
            BEGIN
                EXEC dbo.sp_CompareSnapshotsAndDetectMisses 
                    @ExecutionID = @LatestExecutionID,
                    @DryRun = @DryRun;
            END
            ELSE
            BEGIN
                PRINT 'Snapshot comparison procedure not available. Performing basic snapshot analysis...';
                
                -- Basic snapshot comparison
                DECLARE @PreCount INT = (SELECT COUNT(*) FROM dbo.AnonymizationPreSnapshot WHERE ExecutionID = @LatestExecutionID);
                DECLARE @PostCount INT = (SELECT COUNT(*) FROM dbo.AnonymizationPostSnapshot WHERE ExecutionID = @LatestExecutionID);
                
                PRINT 'Pre-anonymization records: ' + CAST(@PreCount AS VARCHAR);
                PRINT 'Post-anonymization records: ' + CAST(@PostCount AS VARCHAR);
                
                IF @PreCount != @PostCount
                BEGIN
                    INSERT INTO #PatternDetectionResults (Category, TableName, ColumnName, PatternType, RecordCount, Severity, Recommendation)
                    VALUES ('Snapshot', 'Multiple', 'Various', 'Record Count Mismatch', ABS(@PreCount - @PostCount), 'HIGH',
                            'Record counts changed during anonymization. Investigate data integrity.');
                    SET @HighRiskCount = @HighRiskCount + 1;
                END
            END
            
            PRINT 'Snapshot-based verification completed.';
            PRINT '';
            
            -- Execute hash-based verification for original value detection
            PRINT 'Performing hash-based verification to detect surviving original values...';
            IF OBJECT_ID('dbo.sp_VerifyNoOriginalValuesRemain') IS NOT NULL
            BEGIN
                EXEC dbo.sp_VerifyNoOriginalValuesRemain 
                    @ExecutionID = @LatestExecutionID,
                    @DryRun = @DryRun;
            END
            ELSE
            BEGIN
                PRINT 'Hash-based verification procedure not available. Skipping original value detection.';
            END
            
            PRINT 'Hash-based verification completed.';
            PRINT '';
        END
        ELSE
        BEGIN
            PRINT 'No snapshot data found. Falling back to pattern-based verification only.';
            INSERT INTO #PatternDetectionResults (Category, TableName, ColumnName, PatternType, RecordCount, Severity, Recommendation)
            VALUES ('Infrastructure', 'Snapshots', 'Missing', 'No Snapshot Data', 0, 'MEDIUM',
                    'Snapshot infrastructure not used. Cannot verify missed anonymizations.');
            SET @MediumRiskCount = @MediumRiskCount + 1;
            PRINT '';
        END
    END
    ELSE
    BEGIN
        PRINT 'Snapshot tables not available. Using pattern-based verification only.';
        PRINT 'For comprehensive verification, ensure snapshot infrastructure is enabled.';
        INSERT INTO #PatternDetectionResults (Category, TableName, ColumnName, PatternType, RecordCount, Severity, Recommendation)
        VALUES ('Infrastructure', 'Snapshots', 'Missing', 'No Snapshot Tables', 0, 'MEDIUM',
                'Snapshot tables not found. Enable snapshot infrastructure for better verification.');
        SET @MediumRiskCount = @MediumRiskCount + 1;
        PRINT '';
    END

    -- =====================================================================================
    -- ADVANCED PATTERN DETECTION
    -- =====================================================================================
    PRINT 'ADVANCED PATTERN DETECTION AND ANALYSIS';
    PRINT '=======================================================================';

    -- Dynamic pattern detection across all tables
    DECLARE @TableName NVARCHAR(128);
    DECLARE @ColumnName NVARCHAR(128);
    DECLARE @DataType NVARCHAR(128);
    DECLARE @SQL NVARCHAR(MAX);
    DECLARE @PatternCount INT;
    DECLARE @SampleValue NVARCHAR(500);

    DECLARE pattern_cursor CURSOR FOR
    SELECT t.TABLE_NAME, c.COLUMN_NAME, c.DATA_TYPE
    FROM INFORMATION_SCHEMA.TABLES t
    INNER JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
    WHERE t.TABLE_TYPE = 'BASE TABLE'
    AND c.DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext')
    AND t.TABLE_NAME NOT IN ('AnonymizationLog', 'AnonymizationLogBuffer', 'AnonymizationConfig', 
                             'AnonymizationPreSnapshot', 'AnonymizationPostSnapshot')
    AND c.COLUMN_NAME NOT LIKE '%ID'
    AND c.COLUMN_NAME NOT LIKE '%Key'
    ORDER BY t.TABLE_NAME, c.COLUMN_NAME;

    OPEN pattern_cursor;
    FETCH NEXT FROM pattern_cursor INTO @TableName, @ColumnName, @DataType;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        -- =====================================================================================
        -- EMAIL PATTERN DETECTION
        -- =====================================================================================
        -- Check for non-anonymized email patterns
        SET @SQL = 'SELECT @Count = COUNT(*), @Sample = MAX(' + QUOTENAME(@ColumnName) + ') 
                    FROM dbo.' + QUOTENAME(@TableName) + 
                   ' WHERE ' + QUOTENAME(@ColumnName) + ' LIKE ''%@%'' 
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''%' + @EmailSuffix + ''' 
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''%@company.local''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''%@example.com''';
        
        EXEC sp_executesql @SQL, N'@Count INT OUTPUT, @Sample NVARCHAR(500) OUTPUT', 
                          @Count = @PatternCount OUTPUT, @Sample = @SampleValue OUTPUT;
        
        IF @PatternCount > 0
        BEGIN
            INSERT INTO #PatternDetectionResults (Category, TableName, ColumnName, PatternType, SampleValue, RecordCount, Severity, Recommendation)
            VALUES ('Email', @TableName, @ColumnName, 'Non-Anonymized Email', @SampleValue, @PatternCount, 'HIGH',
                    'Real email addresses detected. Re-run email anonymization module.');
            SET @HighRiskCount = @HighRiskCount + 1;
        END

        -- =====================================================================================
        -- IP ADDRESS PATTERN DETECTION
        -- =====================================================================================
        -- Check for public IP addresses (not private ranges)
        SET @SQL = 'SELECT @Count = COUNT(*), @Sample = MAX(' + QUOTENAME(@ColumnName) + ') 
                    FROM dbo.' + QUOTENAME(@TableName) + 
                   ' WHERE ' + QUOTENAME(@ColumnName) + ' LIKE ''%.%.%.%'' 
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''192.168.%'' 
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''10.%'' 
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''172.16.%''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''172.17.%''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''172.18.%''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''172.19.%''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''172.2_.%''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''172.30.%''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''172.31.%''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''127.%''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''0.0.0.0''';
        
        EXEC sp_executesql @SQL, N'@Count INT OUTPUT, @Sample NVARCHAR(500) OUTPUT', 
                          @Count = @PatternCount OUTPUT, @Sample = @SampleValue OUTPUT;
        
        IF @PatternCount > 0
        BEGIN
            INSERT INTO #PatternDetectionResults (Category, TableName, ColumnName, PatternType, SampleValue, RecordCount, Severity, Recommendation)
            VALUES ('Network', @TableName, @ColumnName, 'Public IP Address', @SampleValue, @PatternCount, 'HIGH',
                    'Public IP addresses detected. These may expose network topology.');
            SET @HighRiskCount = @HighRiskCount + 1;
        END

        -- =====================================================================================
        -- DOMAIN NAME DETECTION
        -- =====================================================================================
        -- Check for real domain names
        SET @SQL = 'SELECT @Count = COUNT(*), @Sample = MAX(' + QUOTENAME(@ColumnName) + ') 
                    FROM dbo.' + QUOTENAME(@TableName) + 
                   ' WHERE (' + QUOTENAME(@ColumnName) + ' LIKE ''%.com'' 
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''%.org'' 
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''%.net''
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''%.edu''
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''%.gov'')
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''%example.com'' 
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''%company.local''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''%localhost%''';
        
        EXEC sp_executesql @SQL, N'@Count INT OUTPUT, @Sample NVARCHAR(500) OUTPUT', 
                          @Count = @PatternCount OUTPUT, @Sample = @SampleValue OUTPUT;
        
        IF @PatternCount > 0
        BEGIN
            INSERT INTO #PatternDetectionResults (Category, TableName, ColumnName, PatternType, SampleValue, RecordCount, Severity, Recommendation)
            VALUES ('Domain', @TableName, @ColumnName, 'Real Domain Name', @SampleValue, @PatternCount, 'MEDIUM',
                    'Real domain names detected. Consider anonymizing to prevent organization identification.');
            SET @MediumRiskCount = @MediumRiskCount + 1;
        END

        -- =====================================================================================
        -- PHONE NUMBER DETECTION
        -- =====================================================================================
        -- Check for phone number patterns
        SET @SQL = 'SELECT @Count = COUNT(*), @Sample = MAX(' + QUOTENAME(@ColumnName) + ') 
                    FROM dbo.' + QUOTENAME(@TableName) + 
                   ' WHERE (' + QUOTENAME(@ColumnName) + ' LIKE ''%-%-%'' 
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''(%)%''
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''% % %''
                   OR (' + QUOTENAME(@ColumnName) + ' LIKE ''%[0-9][0-9][0-9][0-9][0-9][0-9][0-9]%'' 
                       AND LEN(' + QUOTENAME(@ColumnName) + ') BETWEEN 7 AND 15))
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''555-%''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''(555)%''';
        
        EXEC sp_executesql @SQL, N'@Count INT OUTPUT, @Sample NVARCHAR(500) OUTPUT', 
                          @Count = @PatternCount OUTPUT, @Sample = @SampleValue OUTPUT;
        
        IF @PatternCount > 0
        BEGIN
            INSERT INTO #PatternDetectionResults (Category, TableName, ColumnName, PatternType, SampleValue, RecordCount, Severity, Recommendation)
            VALUES ('PII', @TableName, @ColumnName, 'Phone Number Pattern', @SampleValue, @PatternCount, 'MEDIUM',
                    'Phone number patterns detected. Consider anonymizing personal contact information.');
            SET @MediumRiskCount = @MediumRiskCount + 1;
        END

        -- =====================================================================================
        -- PERSONAL NAME DETECTION
        -- =====================================================================================
        -- Check for common personal names (basic detection)
        SET @SQL = 'SELECT @Count = COUNT(*), @Sample = MAX(' + QUOTENAME(@ColumnName) + ') 
                    FROM dbo.' + QUOTENAME(@TableName) + 
                   ' WHERE (' + QUOTENAME(@ColumnName) + ' LIKE ''John %'' 
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''Jane %''
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''Michael %''
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''Sarah %''
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''David %''
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''Mary %''
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''Robert %''
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''Jennifer %'')
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''User%''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''Admin%''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''Test%''';
        
        EXEC sp_executesql @SQL, N'@Count INT OUTPUT, @Sample NVARCHAR(500) OUTPUT', 
                          @Count = @PatternCount OUTPUT, @Sample = @SampleValue OUTPUT;
        
        IF @PatternCount > 0
        BEGIN
            INSERT INTO #PatternDetectionResults (Category, TableName, ColumnName, PatternType, SampleValue, RecordCount, Severity, Recommendation)
            VALUES ('PII', @TableName, @ColumnName, 'Personal Name Pattern', @SampleValue, @PatternCount, 'MEDIUM',
                    'Personal name patterns detected. Verify user anonymization is complete.');
            SET @MediumRiskCount = @MediumRiskCount + 1;
        END

        -- =====================================================================================
        -- FILE PATH DETECTION
        -- =====================================================================================
        -- Check for real file paths
        SET @SQL = 'SELECT @Count = COUNT(*), @Sample = MAX(' + QUOTENAME(@ColumnName) + ') 
                    FROM dbo.' + QUOTENAME(@TableName) + 
                   ' WHERE (' + QUOTENAME(@ColumnName) + ' LIKE ''C:\%'' 
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''D:\%''
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''\\%\%''
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''/home/<USER>''
                   OR ' + QUOTENAME(@ColumnName) + ' LIKE ''/var/%'')
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''%AnonymizedData%''
                   AND ' + QUOTENAME(@ColumnName) + ' NOT LIKE ''%anonymized-server%''';
        
        EXEC sp_executesql @SQL, N'@Count INT OUTPUT, @Sample NVARCHAR(500) OUTPUT', 
                          @Count = @PatternCount OUTPUT, @Sample = @SampleValue OUTPUT;
        
        IF @PatternCount > 0
        BEGIN
            INSERT INTO #PatternDetectionResults (Category, TableName, ColumnName, PatternType, SampleValue, RecordCount, Severity, Recommendation)
            VALUES ('Infrastructure', @TableName, @ColumnName, 'Real File Path', @SampleValue, @PatternCount, 'LOW',
                    'Real file paths detected. Consider anonymizing to prevent infrastructure exposure.');
            SET @LowRiskCount = @LowRiskCount + 1;
        END

        SET @TotalRowsProcessed = @TotalRowsProcessed + 1;
        FETCH NEXT FROM pattern_cursor INTO @TableName, @ColumnName, @DataType;
    END

    CLOSE pattern_cursor;
    DEALLOCATE pattern_cursor;

    -- =====================================================================================
    -- RESULTS ANALYSIS AND REPORTING
    -- =====================================================================================
    SET @TotalIssues = @HighRiskCount + @MediumRiskCount + @LowRiskCount;

    PRINT '';
    PRINT 'ADVANCED PATTERN DETECTION RESULTS:';
    PRINT '===================================';
    PRINT 'Total patterns analyzed: ' + CAST(@TotalRowsProcessed AS VARCHAR);
    PRINT 'Total issues found: ' + CAST(@TotalIssues AS VARCHAR);
    PRINT 'High risk issues: ' + CAST(@HighRiskCount AS VARCHAR);
    PRINT 'Medium risk issues: ' + CAST(@MediumRiskCount AS VARCHAR);
    PRINT 'Low risk issues: ' + CAST(@LowRiskCount AS VARCHAR);

    IF @TotalIssues > 0
    BEGIN
        PRINT '';
        PRINT 'DETAILED FINDINGS BY SEVERITY:';
        PRINT '==============================';
        
        -- High severity issues first
        IF @HighRiskCount > 0
        BEGIN
            PRINT '';
            PRINT 'HIGH SEVERITY ISSUES (Immediate Action Required):';
            SELECT Category, TableName, ColumnName, PatternType, RecordCount,
                   LEFT(SampleValue, 50) + CASE WHEN LEN(SampleValue) > 50 THEN '...' ELSE '' END AS SampleValue,
                   Recommendation
            FROM #PatternDetectionResults
            WHERE Severity = 'HIGH'
            ORDER BY RecordCount DESC;
        END
        
        -- Medium severity issues
        IF @MediumRiskCount > 0
        BEGIN
            PRINT '';
            PRINT 'MEDIUM SEVERITY ISSUES (Review Recommended):';
            SELECT Category, TableName, ColumnName, PatternType, RecordCount,
                   LEFT(SampleValue, 50) + CASE WHEN LEN(SampleValue) > 50 THEN '...' ELSE '' END AS SampleValue,
                   Recommendation
            FROM #PatternDetectionResults
            WHERE Severity = 'MEDIUM'
            ORDER BY RecordCount DESC;
        END
        
        -- Low severity issues
        IF @LowRiskCount > 0
        BEGIN
            PRINT '';
            PRINT 'LOW SEVERITY ISSUES (Optional Improvements):';
            SELECT Category, TableName, ColumnName, PatternType, RecordCount,
                   LEFT(SampleValue, 50) + CASE WHEN LEN(SampleValue) > 50 THEN '...' ELSE '' END AS SampleValue,
                   Recommendation
            FROM #PatternDetectionResults
            WHERE Severity = 'LOW'
            ORDER BY RecordCount DESC;
        END
    END
    ELSE
    BEGIN
        PRINT '';
        PRINT 'SUCCESS: No data masking issues detected in advanced pattern analysis.';
    END

    -- =====================================================================================
    -- OVERALL SECURITY ASSESSMENT
    -- =====================================================================================
    DECLARE @SecurityStatus NVARCHAR(50);
    DECLARE @RiskLevel NVARCHAR(20);
    DECLARE @DataLeakageRisk NVARCHAR(20);

    IF @HighRiskCount > 0
    BEGIN
        SET @SecurityStatus = 'FAILED - HIGH RISK DATA DETECTED';
        SET @RiskLevel = 'CRITICAL';
        SET @DataLeakageRisk = 'HIGH';
    END
    ELSE IF @MediumRiskCount > 10
    BEGIN
        SET @SecurityStatus = 'PASSED WITH MAJOR WARNINGS';
        SET @RiskLevel = 'HIGH';
        SET @DataLeakageRisk = 'MEDIUM';
    END
    ELSE IF @MediumRiskCount > 5
    BEGIN
        SET @SecurityStatus = 'PASSED WITH WARNINGS';
        SET @RiskLevel = 'MEDIUM';
        SET @DataLeakageRisk = 'MEDIUM';
    END
    ELSE IF @MediumRiskCount > 0 OR @LowRiskCount > 0
    BEGIN
        SET @SecurityStatus = 'PASSED WITH MINOR ISSUES';
        SET @RiskLevel = 'LOW';
        SET @DataLeakageRisk = 'LOW';
    END
    ELSE
    BEGIN
        SET @SecurityStatus = 'PASSED - FULLY SECURED';
        SET @RiskLevel = 'MINIMAL';
        SET @DataLeakageRisk = 'MINIMAL';
    END

    PRINT '';
    PRINT 'OVERALL SECURITY ASSESSMENT:';
    PRINT '============================';
    PRINT 'Security Status: ' + @SecurityStatus;
    PRINT 'Risk Level: ' + @RiskLevel;
    PRINT 'Data Leakage Risk: ' + @DataLeakageRisk;
    PRINT 'High Risk Issues: ' + CAST(@HighRiskCount AS VARCHAR);
    PRINT 'Medium Risk Issues: ' + CAST(@MediumRiskCount AS VARCHAR);
    PRINT 'Low Risk Issues: ' + CAST(@LowRiskCount AS VARCHAR);

    -- Clean up temporary table
    DROP TABLE #PatternDetectionResults;

    -- =====================================================================================
    -- MODULE COMPLETION
    -- =====================================================================================
    DECLARE @ModuleEndTime DATETIME = GETDATE();
    DECLARE @ModuleExecutionTime INT = DATEDIFF(MILLISECOND, @ModuleStartTime, @ModuleEndTime);

    -- Log module completion
    INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, ExecutionTimeMs, BatchProcessed, Notes)
    VALUES ('Data Masking Verification', 'MODULE_COMPLETE', 'ALL', @TotalRowsProcessed, 'Security Verification', 
            @ModuleExecutionTime, 0, 
            'Status: ' + @SecurityStatus + ' | Risk: ' + @RiskLevel + ' | Issues: H=' + CAST(@HighRiskCount AS VARCHAR) + 
            ', M=' + CAST(@MediumRiskCount AS VARCHAR) + ', L=' + CAST(@LowRiskCount AS VARCHAR));

    PRINT '';
    PRINT '=======================================================================';
    PRINT 'DATA MASKING VERIFICATION MODULE COMPLETED';
    PRINT '=======================================================================';
    PRINT 'Total execution time: ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds';
    PRINT 'Patterns analyzed: ' + CAST(@TotalRowsProcessed AS VARCHAR);
    PRINT 'Security Status: ' + @SecurityStatus;
    PRINT 'Risk Level: ' + @RiskLevel;
    PRINT 'Data Leakage Risk: ' + @DataLeakageRisk;
    PRINT '';
    PRINT 'NEXT STEPS:';
    IF @HighRiskCount > 0
    BEGIN
        PRINT '  1. ✗ DO NOT USE this database until high-risk issues are resolved';
        PRINT '  2. Re-run specific anonymization modules for affected tables';
        PRINT '  3. Investigate why anonymization failed for detected patterns';
        PRINT '  4. Consider additional anonymization techniques';
    END
    ELSE IF @MediumRiskCount > 5
    BEGIN
        PRINT '  1. ⚠ Review medium-risk issues before production use';
        PRINT '  2. Consider additional anonymization passes';
        PRINT '  3. Implement additional data masking for detected patterns';
    END
    ELSE
    BEGIN
        PRINT '  1. ✓ Database passed advanced security verification';
        PRINT '  2. Safe for non-production environments';
        PRINT '  3. Proceed with performance optimization (Module 11)';
        PRINT '  4. Complete final verification (Module 12)';
    END
    PRINT '=======================================================================';

    IF @DryRun = 1 OR @CommitChanges = 0 
        ROLLBACK TRAN 
    ELSE 
        COMMIT TRAN;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK TRAN;
    
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    
    PRINT 'ERROR in Data Masking Verification: ' + @ErrorMessage;
    
    -- Log the error if possible
    IF OBJECT_ID('dbo.AnonymizationLog') IS NOT NULL
    BEGIN
        INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, Notes)
        VALUES ('Data Masking Verification', 'ERROR', 'MODULE_FAILURE', 0, 'Error', @ErrorMessage);
    END
    
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH