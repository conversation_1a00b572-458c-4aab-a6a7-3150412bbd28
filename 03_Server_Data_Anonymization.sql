-- =====================================================================================
-- COMPLETEVIEW DATABASE ANONYMIZATION - STANDALONE MODULE
-- File: 03_Server_Data_Anonymization.sql
-- =====================================================================================
-- This is a standalone, pre-configured SQL module that can be executed directly
-- in SQL Server Management Studio without requiring PowerShell configuration.
-- 
-- IMPORTANT: 
-- 1. BACKUP YOUR DATABASE BEFORE RUNNING THIS SCRIPT
-- 2. Review the configuration values below before execution
-- 3. Execute modules in numerical order (01, 02, 03, etc.)
-- =====================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON;

-- =====================================================================================
-- CENTRALIZED CONFIGURATION LOADER
-- =====================================================================================

-- Verify configuration prerequisites
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig')
BEGIN
    RAISERROR('AnonymizationConfig table not found. Please run Module 01 (Setup and Validation) first.', 16, 1);
END

IF OBJECT_ID('dbo.fn_GetConfigValue') IS NULL
BEGIN
    RAISERROR('Configuration helper function fn_GetConfigValue not found. Please run Module 01 (Setup and Validation) first.', 16, 1);
END

-- Load configuration values from centralized table
DECLARE @ExecutionID VARCHAR(50) = 'MODULE03_' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '');
DECLARE @ModuleName VARCHAR(100) = 'Server Data Anonymization';

PRINT 'Starting ' + @ModuleName + '...';
PRINT 'Execution ID: ' + @ExecutionID;

DECLARE @AnonymizationSeed INT = CAST(dbo.fn_GetConfigValue('AnonymizationSeed') AS INT);
DECLARE @AnonymizedDomain NVARCHAR(100) = dbo.fn_GetConfigValue('AnonymizedDomain');
DECLARE @EmailSuffix NVARCHAR(100) = dbo.fn_GetConfigValue('EmailSuffix');
DECLARE @DefaultPassword NVARCHAR(100) = dbo.fn_GetConfigValue('DefaultPassword');
DECLARE @BatchSize INT = CAST(dbo.fn_GetConfigValue('BatchSize') AS INT);
DECLARE @DryRun BIT = CAST(dbo.fn_GetConfigValue('DryRun') AS BIT);
DECLARE @CommitChanges BIT = CAST(dbo.fn_GetConfigValue('CommitChanges') AS BIT);
DECLARE @UsePerformanceOptimizations BIT = CAST(dbo.fn_GetConfigValue('UsePerformanceOptimizations') AS BIT);
DECLARE @RequireRecentBackup BIT = CAST(dbo.fn_GetConfigValue('RequireRecentBackup') AS BIT);
DECLARE @BackupMaxAgeDays INT = CAST(dbo.fn_GetConfigValue('BackupMaxAgeDays') AS INT);

-- IP and path configuration
DECLARE @CVE_IPRange_Cameras NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Cameras');
DECLARE @CVE_IPRange_Servers NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Servers');
DECLARE @CVE_IPRange_SessionManager NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_SessionManager');
DECLARE @CVE_FilePath_Prefix NVARCHAR(100) = dbo.fn_GetConfigValue('CVE_FilePath_Prefix');
DECLARE @CVE_UNCPath_Prefix NVARCHAR(100) = dbo.fn_GetConfigValue('CVE_UNCPath_Prefix');
DECLARE @CVE_StreamProfile_Prefix NVARCHAR(50) = dbo.fn_GetConfigValue('CVE_StreamProfile_Prefix');

-- Anonymous username variable
DECLARE @AnonymousUsername NVARCHAR(100) = dbo.fn_GetConfigValue('AnonymousUsername');
IF @AnonymousUsername IS NULL OR @AnonymousUsername = ''
    SET @AnonymousUsername = 'anonymous_user';

-- Aliases for backward compatibility
DECLARE @ServerIPRange NVARCHAR(20) = @CVE_IPRange_Servers;

PRINT 'Configuration loaded from centralized table for Server Data Anonymization';

-- =====================================================================================
-- SERVER DATA ANONYMIZATION MODULE
-- =====================================================================================

PRINT '=======================================================================';
PRINT 'SERVER DATA ANONYMIZATION MODULE';
PRINT '=======================================================================';

BEGIN TRY
    BEGIN TRAN;

    -- Create pre-anonymization snapshot
    PRINT 'Creating pre-anonymization snapshot...';
    IF OBJECT_ID('dbo.sp_CreatePreAnonymizationSnapshot') IS NOT NULL
    BEGIN
        EXEC dbo.sp_CreatePreAnonymizationSnapshot 
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END

    -- Initialize module variables
    DECLARE @RowsAffected INT;
    DECLARE @TotalRowsProcessed INT = 0;
    DECLARE @ModuleStartTime DATETIME = GETDATE();
    DECLARE @OperationStart DATETIME;

    PRINT 'Server anonymization started at: ' + CONVERT(VARCHAR, @ModuleStartTime, 121);
    PRINT 'Dry Run Mode: ' + CASE WHEN @DryRun = 1 THEN 'ENABLED' ELSE 'DISABLED' END;
    PRINT '';

    -- =====================================================================================
    -- 1. ANONYMIZE SERVER DATA
    -- =====================================================================================
    PRINT 'Anonymizing server data...';
    SET @OperationStart = GETDATE();

    -- Check if Server table exists
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'Server')
    BEGIN
        DECLARE @ServerRowCount INT = (SELECT COUNT(*) FROM dbo.Server);
        
        IF @ServerRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@ServerRowCount AS VARCHAR) + ' server records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Server data anonymization preview:';
                SELECT TOP 10
                    ServerId,
                    'SRV-' + RIGHT('000000' + CAST(ROW_NUMBER() OVER (ORDER BY ServerId) AS VARCHAR), 6) AS New_DisplayName,
                    @ServerIPRange + '.' + 
                        CAST((ROW_NUMBER() OVER (ORDER BY ServerId) - 1) / 254 + 1 AS VARCHAR) + '.' + 
                        CAST((ROW_NUMBER() OVER (ORDER BY ServerId) - 1) % 254 + 1 AS VARCHAR) AS New_IP,
                    CASE 
                        WHEN @DefaultPassword = '[ACCOUNT_DISABLED]' THEN '[ACCOUNT_DISABLED]'
                        WHEN @DefaultPassword = '[SALTED_HASH]' THEN '[SALTED_HASH_GENERATED]'
                        WHEN @DefaultPassword IS NULL THEN NULL
                        ELSE @DefaultPassword
                    END AS New_Password,
                    @AnonymousUsername AS New_UserName
                FROM dbo.Server;
                
                SET @RowsAffected = @ServerRowCount;
            END
            ELSE
            BEGIN
                -- Anonymize server display names
                DECLARE @NameUpdateCount INT = 0;
                WITH NumberedServers AS (
                    SELECT ServerId, DisplayName,
                           ROW_NUMBER() OVER (ORDER BY ServerId) AS RowNum
                    FROM dbo.Server
                    WHERE DisplayName IS NOT NULL
                )
                UPDATE dbo.Server
                SET DisplayName = 'SRV-' + RIGHT('000000' + CAST(NumberedServers.RowNum AS VARCHAR), 6)
                FROM dbo.Server
                INNER JOIN NumberedServers ON dbo.Server.ServerId = NumberedServers.ServerId;
                SET @NameUpdateCount = @@ROWCOUNT;
                
                -- Anonymize server IP addresses
                DECLARE @IPUpdateCount INT = 0;
                WITH NumberedServerIPs AS (
                    SELECT ServerId, IP,
                           ROW_NUMBER() OVER (ORDER BY ServerId) AS RowNum
                    FROM dbo.Server
                    WHERE IP IS NOT NULL
                )
                UPDATE dbo.Server
                SET IP = @ServerIPRange + '.' + 
                    CAST((NumberedServerIPs.RowNum - 1) / 254 + 1 AS VARCHAR) + '.' + 
                    CAST((NumberedServerIPs.RowNum - 1) % 254 + 1 AS VARCHAR)
                FROM dbo.Server
                INNER JOIN NumberedServerIPs ON dbo.Server.ServerId = NumberedServerIPs.ServerId;
                SET @IPUpdateCount = @@ROWCOUNT;
                
                -- Anonymize server usernames
                DECLARE @UsernameUpdateCount INT = 0;
                UPDATE dbo.Server 
                SET UserName = @AnonymousUsername
                WHERE UserName IS NOT NULL;
                SET @UsernameUpdateCount = @@ROWCOUNT;
                
                -- Anonymize passwords using secure strategy
                DECLARE @PasswordUpdateCount INT = 0;
                IF @DefaultPassword = '[ACCOUNT_DISABLED]'
                BEGIN
                    UPDATE dbo.Server 
                    SET Password = '[ACCOUNT_DISABLED]'
                    WHERE Password IS NOT NULL;
                    SET @PasswordUpdateCount = @@ROWCOUNT;
                END
                ELSE IF @DefaultPassword = '[SALTED_HASH]'
                BEGIN
                    UPDATE dbo.Server 
                    SET Password = '$2b$12$' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '') + 'SecureHash'
                    WHERE Password IS NOT NULL;
                    SET @PasswordUpdateCount = @@ROWCOUNT;
                END
                ELSE IF @DefaultPassword IS NULL
                BEGIN
                    UPDATE dbo.Server 
                    SET Password = NULL
                    WHERE Password IS NOT NULL;
                    SET @PasswordUpdateCount = @@ROWCOUNT;
                END
                ELSE
                BEGIN
                    UPDATE dbo.Server 
                    SET Password = @DefaultPassword
                    WHERE Password IS NOT NULL;
                    SET @PasswordUpdateCount = @@ROWCOUNT;
                END
                
                -- Buffer operations for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES 
                    ('Server Data', 'Server', 'DisplayName', @NameUpdateCount, 'Sequential Naming', @OperationStart, GETDATE(), 
                     'Server display names anonymized with SRV-### pattern'),
                    ('Server Data', 'Server', 'IP', @IPUpdateCount, 'Sequential IP Assignment', @OperationStart, GETDATE(), 
                     'Server IP addresses anonymized using ' + @ServerIPRange + '.x.x range'),
                    ('Server Data', 'Server', 'UserName', @UsernameUpdateCount, 'Generic Replacement', @OperationStart, GETDATE(), 
                     'Server usernames replaced with anonymous username'),
                    ('Server Data', 'Server', 'Password', @PasswordUpdateCount, 'Secure Strategy', @OperationStart, GETDATE(), 
                     'Server passwords processed using strategy: ' + ISNULL(@DefaultPassword, 'NULL'));
                
                SET @RowsAffected = @NameUpdateCount + @IPUpdateCount + @UsernameUpdateCount + @PasswordUpdateCount;
            END
            
            SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;
            PRINT 'Server data anonymized. Rows affected: ' + CAST(@RowsAffected AS VARCHAR);
        END
        ELSE
        BEGIN
            PRINT 'No server records found to anonymize.';
            SET @RowsAffected = 0;
        END
    END
    ELSE
    BEGIN
        PRINT 'Server table not found in database.';
        SET @RowsAffected = 0;
    END

    -- =====================================================================================
    -- 2. ANONYMIZE SYSTEM CONFIGURATION DATA
    -- =====================================================================================
    PRINT '';
    PRINT 'Anonymizing system configuration data...';
    SET @OperationStart = GETDATE();

    -- Anonymize system settings that may contain server information
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'SystemSettings')
    BEGIN
        DECLARE @SettingsRowCount INT = (SELECT COUNT(*) FROM dbo.SystemSettings 
                                        WHERE SettingValue LIKE '%server%' 
                                        OR SettingValue LIKE '%host%' 
                                        OR SettingValue LIKE '%admin%');
        
        IF @SettingsRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@SettingsRowCount AS VARCHAR) + ' system settings containing server references.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: System settings that would be anonymized:';
                SELECT TOP 10
                    SettingName,
                    SettingValue AS Current_Value,
                    '[REDACTED]' AS New_Value
                FROM dbo.SystemSettings 
                WHERE SettingValue LIKE '%server%' 
                OR SettingValue LIKE '%host%' 
                OR SettingValue LIKE '%admin%';
                
                SET @RowsAffected = @SettingsRowCount;
            END
            ELSE
            BEGIN
                UPDATE dbo.SystemSettings 
                SET SettingValue = '[REDACTED]'
                WHERE SettingValue LIKE '%server%' 
                OR SettingValue LIKE '%host%' 
                OR SettingValue LIKE '%admin%';
                
                SET @RowsAffected = @@ROWCOUNT;
                
                -- Log changes
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Server Data', 'SystemSettings', 'SettingValue', @RowsAffected, 'Generic Replacement', @OperationStart, GETDATE(), 
                        'System settings with server references anonymized');
                
                PRINT 'Updated ' + CAST(@RowsAffected AS VARCHAR) + ' system settings';
            END
            
            SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;
            PRINT 'System settings anonymized. Rows affected: ' + CAST(@RowsAffected AS VARCHAR);
        END
        ELSE
        BEGIN
            PRINT 'No system settings with server references found.';
            SET @RowsAffected = 0;
        END
    END
    ELSE
    BEGIN
        PRINT 'SystemSettings table not found in database.';
        SET @RowsAffected = 0;
    END

    -- =====================================================================================
    -- 3. ANONYMIZE LICENSE AND ACTIVATION DATA
    -- =====================================================================================
    PRINT '';
    PRINT 'Anonymizing license and activation data...';
    SET @OperationStart = GETDATE();

    -- Anonymize license server information
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'LicenseInfo')
    BEGIN
        DECLARE @LicenseRowCount INT = (SELECT COUNT(*) FROM dbo.LicenseInfo);
        
        IF @LicenseRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@LicenseRowCount AS VARCHAR) + ' license records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: License data anonymization preview:';
                SELECT TOP 10
                    LicenseID,
                    '[REDACTED-LICENSE-KEY]' AS New_LicenseKey,
                    'license-server.example.com' AS New_LicenseServer,
                    '[REDACTED]' AS New_ActivationCode
                FROM dbo.LicenseInfo;
                
                SET @RowsAffected = @LicenseRowCount;
            END
            ELSE
            BEGIN
                UPDATE dbo.LicenseInfo 
                SET LicenseKey = '[REDACTED-LICENSE-KEY]',
                    LicenseServer = 'license-server.example.com',
                    ActivationCode = '[REDACTED]'
                WHERE LicenseKey IS NOT NULL OR LicenseServer IS NOT NULL OR ActivationCode IS NOT NULL;
                
                SET @RowsAffected = @@ROWCOUNT;
                
                -- Log changes
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Server Data', 'LicenseInfo', 'LicenseData', @RowsAffected, 'Generic Replacement', @OperationStart, GETDATE(), 
                        'License keys and server information anonymized');
                
                PRINT 'Updated ' + CAST(@RowsAffected AS VARCHAR) + ' license records';
            END
            
            SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;
            PRINT 'License data anonymized. Rows affected: ' + CAST(@RowsAffected AS VARCHAR);
        END
        ELSE
        BEGIN
            PRINT 'No license records found to anonymize.';
            SET @RowsAffected = 0;
        END
    END
    ELSE
    BEGIN
        PRINT 'LicenseInfo table not found in database.';
        SET @RowsAffected = 0;
    END

    -- =====================================================================================
    -- 4. ANONYMIZE DATABASE CONNECTION STRINGS
    -- =====================================================================================
    PRINT '';
    PRINT 'Anonymizing database connection information...';
    SET @OperationStart = GETDATE();

    -- Anonymize any stored database connection strings
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'DatabaseConnections')
    BEGIN
        DECLARE @DbConnRowCount INT = (SELECT COUNT(*) FROM dbo.DatabaseConnections);
        
        IF @DbConnRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@DbConnRowCount AS VARCHAR) + ' database connection records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Database connection anonymization preview:';
                SELECT TOP 10
                    ConnectionID,
                    'db-server.example.com' AS New_ServerName,
                    '[REDACTED]' AS New_ConnectionString,
                    @AnonymousUsername AS New_Username,
                    CASE 
                        WHEN @DefaultPassword = '[ACCOUNT_DISABLED]' THEN '[ACCOUNT_DISABLED]'
                        WHEN @DefaultPassword = '[SALTED_HASH]' THEN '[SALTED_HASH_GENERATED]'
                        WHEN @DefaultPassword IS NULL THEN NULL
                        ELSE '[REDACTED]'
                    END AS New_Password
                FROM dbo.DatabaseConnections;
                
                SET @RowsAffected = @DbConnRowCount;
            END
            ELSE
            BEGIN
                -- Apply secure password strategy to database connections
                DECLARE @SecurePassword NVARCHAR(100);
                IF @DefaultPassword = '[ACCOUNT_DISABLED]'
                    SET @SecurePassword = '[ACCOUNT_DISABLED]';
                ELSE IF @DefaultPassword = '[SALTED_HASH]'
                    SET @SecurePassword = '$2b$12$' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '') + 'DbSecure';
                ELSE IF @DefaultPassword IS NULL
                    SET @SecurePassword = NULL;
                ELSE
                    SET @SecurePassword = @DefaultPassword;
                
                UPDATE dbo.DatabaseConnections 
                SET ServerName = 'db-server.example.com',
                    ConnectionString = '[REDACTED]',
                    Username = @AnonymousUsername,
                    Password = @SecurePassword;
                
                SET @RowsAffected = @@ROWCOUNT;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Server Data', 'DatabaseConnections', 'ConnectionData', @RowsAffected, 'Generic Replacement', @OperationStart, GETDATE(), 
                        'Database connection strings and credentials anonymized');
                
                PRINT 'Updated ' + CAST(@RowsAffected AS VARCHAR) + ' database connection records';
            END
            
            SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;
            PRINT 'Database connection data anonymized. Rows affected: ' + CAST(@RowsAffected AS VARCHAR);
        END
        ELSE
        BEGIN
            PRINT 'No database connection records found to anonymize.';
            SET @RowsAffected = 0;
        END
    END
    ELSE
    BEGIN
        PRINT 'DatabaseConnections table not found in database.';
        SET @RowsAffected = 0;
    END

    -- =====================================================================================
    -- MODULE COMPLETION
    -- =====================================================================================
    DECLARE @ModuleEndTime DATETIME = GETDATE();
    DECLARE @ModuleExecutionTime INT = DATEDIFF(MILLISECOND, @ModuleStartTime, @ModuleEndTime);

    -- Flush aggregated logging buffer to reduce transaction log bloat
    IF OBJECT_ID('dbo.sp_FlushAnonymizationBuffer') IS NOT NULL
    BEGIN
        EXEC dbo.sp_FlushAnonymizationBuffer @DryRun = @DryRun;
    END

    -- Log module completion
    INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, ExecutionTime, Notes)
    VALUES ('Server Data', 'MODULE_COMPLETE', 'ALL', @TotalRowsProcessed, 'Module Execution', 
            @ModuleEndTime, 
            'Server data anonymization completed in ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds');

    PRINT '';
    PRINT '=======================================================================';
    PRINT 'SERVER DATA ANONYMIZATION MODULE COMPLETED';
    PRINT '=======================================================================';
    PRINT 'Total execution time: ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds';
    PRINT 'Total rows processed: ' + CAST(@TotalRowsProcessed AS VARCHAR);
    PRINT 'Operations completed: Server data, system settings, license info, database connections';
    PRINT 'Dry run mode: ' + CASE WHEN @DryRun = 1 THEN 'YES (No data modified)' ELSE 'NO (Data modified)' END;
    PRINT '=======================================================================';

    -- Create post-anonymization snapshot
    IF @DryRun = 0 AND OBJECT_ID('dbo.sp_CreatePostAnonymizationSnapshot') IS NOT NULL
    BEGIN
        PRINT 'Creating post-anonymization snapshot...';
        EXEC dbo.sp_CreatePostAnonymizationSnapshot 
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END

    IF @DryRun = 1 OR @CommitChanges = 0 
        ROLLBACK TRAN 
    ELSE 
        COMMIT TRAN;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK TRAN;
    
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    
    PRINT 'ERROR in Server Data Anonymization: ' + @ErrorMessage;
    
    -- Log the error if possible
    IF OBJECT_ID('dbo.AnonymizationLog') IS NOT NULL
    BEGIN
        INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, Notes)
        VALUES ('Server Data', 'ERROR', 'MODULE_FAILURE', 0, 'Error', @ErrorMessage);
    END
    
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH